/*
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package edu.memphis.ccrg.linars;

import edu.memphis.ccrg.lida.attentioncodelets.NeighborhoodAttentionCodelet;
import edu.memphis.ccrg.lida.data.TermUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PamNodeImpl;
import edu.memphis.ccrg.lida.pam.tasks.IsaPamTask;
import edu.memphis.ccrg.lida.workspace.WorkspaceImpl;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.opennars.control.DerivationContext;
import org.opennars.control.concept.ProcessAnticipation;
import org.opennars.entity.*;
import org.opennars.entity.Stamp.BaseEntry;
import org.opennars.inference.LocalRules;
import org.opennars.inference.TemporalRules;
import org.opennars.inference.TruthFunctions;
import org.opennars.io.Parser;
import org.opennars.io.Symbols;
import org.opennars.io.events.Events;
import org.opennars.language.*;
import org.opennars.main.Debug;
import org.opennars.operator.FunctionOperator;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;
import org.opennars.plugin.mental.InternalExperience;
import org.opennars.storage.Bag1;

import java.util.*;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.*;
import static edu.memphis.ccrg.lida.pam.PamImpl0.getDefultTask;
import static org.opennars.inference.LocalRules.*;

/**
 *
 * <AUTHOR> Hammer
 */
public class ProcessGoal {
    private static Memory memory;

    /**
     * To accept a new goal, and check for revisions and realization, then
     * decide whether to actively pursue it, potentially executing in case of an operation goal
     * 要接受一个新的目标，并检查修订和实现，然后决定是否积极追求它，如果有运营目标，则有可能执行
     * todo 需标记目前执行的目标，如果子目标，还需整个目标体系，另外目标竞争机制，优先级，执行时间
     * @param concept The concept of the goal
     * @param nal The derivation context
     * @param task The goal task to be processed
     */
    public static void processGoal(final Concept concept, final DerivationContext nal, final Task task) {
        getMem();

        final Sentence goal = task.sentence;
		//排除重复  // revise with the existing desire values   用已存在的期望值观进行修正
        final Task oldGoalT = concept.selectCandidate(task, concept.desires, nal.time);
        Sentence oldGoal = null;
        final Stamp newStamp = goal.stamp;
        if (oldGoalT != null) {
            oldGoal = oldGoalT.sentence;
            final Stamp oldStamp = oldGoal.stamp;
            if (newStamp.equals(oldStamp,false,false,true)) {
                return; // duplicate 重复
            }
        }
        Task beliefT = null;
        if(task.aboveThreshold()) {
            for (final Task iQuest : concept.quests ) {
                trySolution(task.sentence, iQuest, nal, true);
            }
            beliefT = concept.selectCandidate(task, concept.beliefs, nal.time);
            // check if the Goal is already satisfied manipulate budget of belief，
            // 检查目标是否已满足（调整预算）操纵信念的预算。可满足则新建新预算任务
            if (beliefT != null) {
                trySolution(beliefT.sentence, task, nal, true);
            }
        }
        // todo 重复目标，判断是否已被修订
        if (oldGoalT != null && !goal.isRevisied && revisible(goal, oldGoal, nar.narParameters)) {
            final Stamp oldStamp = oldGoal.stamp;
            nal.setTheNewStamp(newStamp, oldStamp, nal.time.time());
            final Sentence projectedGoal00 = oldGoal.projection(task.sentence.getOccurenceTime(), newStamp.getOccurrenceTime(), concept.memory);
            if (projectedGoal00 != null) {
                nal.setCurrentBelief(projectedGoal00);
                final boolean wasRevised = revision(task.sentence, projectedGoal00, concept, false, nal);
                if (wasRevised) {
                    /* It was revised, so there is a new task for which this method will be called with higher/lower desire.
                     * We return because it is not allowed to go on directly due to decision making.
                     * see https://groups.google.com/forum/#!topic/open-nars/lQD0no2ovx4
                     * 它进行了修订，因此有一个新任务，将以更高/更低的期望调用此方法。返回因为不允许直接决策。
					 */
                    goal.isRevisied = true;
                    return;
                }
            }
        }
        final Stamp s2 = goal.stamp.clone();
        s2.setOccurrenceTime(nal.time.time());
        if(s2.after(task.sentence.stamp, nar.narParameters.DURATION)) {
            // this task is not up to date we have to project it first  这个任务不是最新的，我们必须先投影它
            final Sentence projGoal = task.sentence.projection(nal.time.time(), nar.narParameters.DURATION, memory);
            if(projGoal != null && projGoal.truth.getExpectation() > nar.narParameters.DECISION_THRESHOLD) {
                // keep goal updated    保持目标更新
                nal.singlePremiseTask(projGoal, task.budget.clone());

                // we don't return here, allowing "roundtrips now",
                // relevant for executing multiple steps of learned implication chains
                // 我们不在此返回，允许“立即往返”，与执行多个步骤的学习隐含链有关
			}
        }
        if (!task.aboveThreshold()) {return;}
        // we dont know anything about that goal yet    我们对该目标一无所知
        double AntiSatisfaction = 0.5f;// 含义：反满意度
        if (beliefT != null) {
            final Sentence belief = beliefT.sentence;
            final Sentence projectedBelief = belief.projection(task.sentence.getOccurenceTime(), nar.narParameters.DURATION, memory);
            AntiSatisfaction = task.sentence.truth.getExpDifAbs(projectedBelief.truth);
        }
        task.setPriority(task.getPriority() * (float)AntiSatisfaction);
        if (!task.aboveThreshold()) {return;}
        final boolean isFullfilled = AntiSatisfaction < nar.narParameters.SATISFACTION_TRESHOLD;
        final Sentence projectedGoal01 = goal.projection(nal.time.time(), nal.time.time(), memory);
        if (!(projectedGoal01 != null && task.aboveThreshold() && !isFullfilled)) {return;}
        final boolean inhitedBabblingGoal = task.isInput() && !concept.allowBabbling;
        if(inhitedBabblingGoal) {return;}

        bestReactionForGoal(concept, nal, projectedGoal01, task);
        // 新增到目标列表，期望列表，已执行过的
        concept.addToTable(task, false, concept.desires, nar.narParameters.CONCEPT_GOALS_MAX,
                Events.ConceptGoalAdd.class, Events.ConceptGoalRemove.class);
        // 从任务推出内部经验，如believe、want等
        InternalExperience.InternalExperienceFromTask(concept.memory, task, false, nal.time);

        // 生成 <?how=/>g>? 问题对目标。原版都false
//        questionFromGoal(task, nal);
//        if(!(task.sentence.getTerm() instanceof Operation)) {
//            return;
//        }
        // 执行具身操作。动机的关联具身操作，现在单个的都直接执行了，这里到达不了
        // 原版操作动机的前提要求很高，四个分句，一般单操作都在这执行了。带变量也不能执行
        // todo 推理一个执行一个，无视竞争？过于实时。推理执行分开，推理后竞争再执行？权衡两者
        // 竞争是决策推理阶段问题？一旦决策出动机，就实时执行？
        // <(*,回应,(*,$事物,去,哪里,了)) <-> (^say,(^search,(*,$事物,去,哪里,了)))>
        // 两句的结果都会输出，有操作符和变量不输出，但“回应”无操作符，会直接输出。
        // ！！！已经在getActRoot执行，不用这里执行
//        processOperationGoal(projectedGoal, nal, concept, oldGoalT, task);
    }

    private static void getMem() {
        WorkspaceBuffer buffer = (WorkspaceBuffer) ((WorkspaceImpl) pam.getListener()).getSubmodule(ModuleName.GoalGraph);
        NodeStructure nsmem = buffer.getBufferContent(null);
        memory = (Memory) nsmem;
    }

    /**
     * To process an operation for potential execution。only called by processGoal
     * 处理可能执行的操作，具身，只被processGoal调用
     * @param projectedGoal The current goal
     * @param nal The derivation context
     * @param concept The concept of the current goal
     * @param oldGoalT The best goal in the goal table
     * @param task The goal task
     */
    protected static void processOperationGoal(final Sentence projectedGoal, final DerivationContext nal,
                                               final Concept concept, final Task oldGoalT, final Task task) {
        if(projectedGoal.truth.getExpectation() > nar.narParameters.DECISION_THRESHOLD) {
            // see whether the goal evidence is fully included in the old goal, if yes don't execute.as execution for this reason already happened (or did not since there was evidence against it).
            // 查看目标证据是否已完全包含在旧目标中，如果是，则不执行。因为已经执行了这个推理（或者没有执行，因为有证据反对）
            final Set<BaseEntry> oldEvidence = new LinkedHashSet<>();
            boolean Subset = false;
            if(oldGoalT != null) {
                Subset = true;
                for(final BaseEntry l: oldGoalT.sentence.stamp.evidentialBase) {
                    oldEvidence.add(l);
                }
                for(final BaseEntry l: task.sentence.stamp.evidentialBase) {
                    if(!oldEvidence.contains(l)) {
                        Subset = false;
                        break;
                    }
                }
            }
            if(!Subset && !executeOperation(nal, task)) {
                concept.memory.emit(Events.UnexecutableGoal.class, task, concept, nal);
//                return; //it was made true by itself //它本身就是真实的
            }
        }
    }

    /**
     * Generate <?how =/> g>? question for g! goal.
     * only called by processGoal
     * 生成 <?how=/>g>? 问题对目标
     * @param task the task for which the question should be processed
     * @param nal The derivation context
     */
    public static void questionFromGoal(final Task task, final DerivationContext nal) {
        if(nar.narParameters.QUESTION_GENERATION_ON_DECISION_MAKING || nar.narParameters.HOW_QUESTION_GENERATION_ON_DECISION_MAKING) {
            //ok, how can we achieve it? add a question of whether it is fullfilled
            // 我们如何实现呢？ 添加一个是否满足的问题
            final List<Term> qu = new ArrayList<>();
            if(nar.narParameters.HOW_QUESTION_GENERATION_ON_DECISION_MAKING) {
                if(!(task.sentence.term instanceof Equivalence) && !(task.sentence.term instanceof Implication)) {
                    final Variable how = new Variable("?how");
                    //Implication imp=Implication.make(how, task.sentence.term, TemporalRules.ORDER_CONCURRENT);
                    final Implication imp2 = Implication.make(how, task.sentence.term, TemporalRules.ORDER_FORWARD);
                    //qu.add(imp);
                    if(!(task.sentence.term instanceof Operation)) {
                        qu.add(imp2);
                    }
                }
            }
            if(nar.narParameters.QUESTION_GENERATION_ON_DECISION_MAKING) {
                qu.add(task.sentence.term);
            }
            for(final Term q : qu) {
                if(q != null) {
                    final Stamp st = new Stamp(task.sentence.stamp, nal.time.time());
                    st.setOccurrenceTime(task.sentence.getOccurenceTime()); //set tense of question to goal tense将问题时态设定为目标时态
                    final Sentence s = new Sentence(
                        q,
                        Symbols.QUESTION_MARK,
                        null,
                        st);

                    if(s != null) {
                        final BudgetValue budget=new BudgetValue(task.getPriority()* nar.narParameters.CURIOSITY_DESIRE_PRIORITY_MUL,
                            task.getDurability()* nar.narParameters.CURIOSITY_DESIRE_DURABILITY_MUL,
                            1, nar.narParameters);
                        nal.singlePremiseTask(s, budget);
                    }
                }
            }
        }
    }

    /**
     * When a goal is processed, use the best memorized reaction
     * that is applicable to the current context (recent events) in case that it exists.
     * This is a special case of the choice rule and allows certain behaviors to be automated.
     *  处理目标时，使用适用于当前环境（最近事件）的最佳记忆反应（如果存在）。
     *  这是选择规则的一种特殊情况，它可以使某些行为【自动化】。原版都是多分句带间隔时序
     * @param concept The concept of the goal to realize
     * @param nal The derivation context
     * @param projectedGoal3 The current goal
     * @param task The goal task
     */
    public static void bestReactionForGoal(final Concept concept, final DerivationContext nal,
                                           final Sentence projectedGoal3, final Task task) {
        // useful as it is represents a goal concept that can hold important procedure knowledge
        // 它有用，因为它表示可以保留重要程序知识的目标概念。Quality + 0.1
         concept.incAcquiredQuality();
		// 1. pull up variable based preconditions from component concepts without replacing them1
        // 从组件概念中提取基于变量的前提条件，而不用替换它们
        Map<Term, Integer> ret = (projectedGoal3.getTerm()).countTermRecursively(null);
        List<Task> generalPreconditions = new ArrayList<>();
        for(Term t : ret.keySet()) {
            // the concept to pull preconditions from
            // 从中提取前提条件的概念。用全局nars缓存，有些是继承类等概念，目标mem里不一定有
            final Concept get_concept = nar.memory.concept(t);
            // target concept does not exist or is the same as the goal concept
            // 目标概念不存在或与目标概念相同
            if(get_concept == null || get_concept == concept) {continue;}
            // pull variable based preconditions from component concepts
            // 从组件概念中获取基于变量的前提条件，可对变量句进行实例化。如（26加8）的加关联变量句($x加$y)
            synchronized(get_concept) {
                boolean useful_component = false;
                for(Task precon2 : get_concept.general_executable_preconditions) {
                    // check whether the conclusion matches
                    // 检查结论是否匹配，可包含变量。原本是变量统一方法，可做匹配判断
                    if(Variables.findSubstitute(memory.randomNumber, Symbols.VAR_INDEPENDENT,
                            ((Statement)precon2.sentence.term).getPredicate(), projectedGoal3.term, new LinkedHashMap<>(), new LinkedHashMap<>())) {
                        for(Task prec : get_concept.general_executable_preconditions) {
                            generalPreconditions.add(prec);
                            useful_component = true;
                        }
                    }
                }
                if(useful_component) {
                    //useful as it contributed predictive hypotheses 有用，因为它有助于预测假设
                    get_concept.incAcquiredQuality();
                }
            }
        }
        //  2. Accumulate all general preconditions of itself too and create list for anticipations
        //  也积累自身的所有一般前提条件，并创建预期清单。都是顺承等操作相关，动机分解、动机变量实例化等处理在推理部分
        //  体感动机一般只有自身相关，因为体感组件没有关联的变量句；也没法实例化，需额外处理
        //  运算26+8则没有自身相关，有组件相关的变量句，结合可实例化，做时序执行参数。但一般是实时任务，不能直接被满足（体感任务可以）
		generalPreconditions.addAll(concept.general_executable_preconditions);

        //  3. For the more specific hypotheses first and then the general
        //  首先针对更具体的假设，然后针对一般假设
        applyRule(concept, nal, projectedGoal3, task, generalPreconditions);

        applyRule(concept, nal, projectedGoal3, task, concept.executable_preconditions);
    }

    public static void applyRule(Concept concept, DerivationContext nal, Sentence projectedGoal,
                                 Task task, List<Task> table) {
        Map<Operation,List<ExecutablePrecondition>> anticipationsToMake = new LinkedHashMap<>();
        // 4. Apply choice rule, using the highest truth expectation solution and anticipate the results
        //  应用选择规则，使用最高真值期望值解决方案并预测结果
        List<ExecutablePrecondition> bestOpWithMetas = calcBestExecutablePrecondition(nal, concept,
                projectedGoal, table, anticipationsToMake, task);
        // 非时序的多个操作，如状态与操作的组合，不能直接当时序执行，也要取中间生成新动机，连续执行多个单操作等
        // 原版的非实时处理，都派生后，放任务序列，再统一处理，判断区分执行。便捷但不实时，也非并行
        // 全部构建成时序？操作前中后都可有状态，可并列可串行。大型时序，一句话时序，甚至单个操作时序
        // 会怎样=预测，要怎样=动机条件。语句内，无论序列或合取，左边条件都是需要满足，右边结论都是预测结果
        if (bestOpWithMetas != null){
            if (bestOpWithMetas.size() > 1 && bestOpWithMetas.size() < 4) {
                memory.multiAction = true;
                System.out.println("multiAction----------------- size :" + bestOpWithMetas.size());
            }
            // 动作序列执行
            for(ExecutablePrecondition bestOpWithMeta: bestOpWithMetas){
                // 5. And executing it, also forming an expectation about the result
                // 并直接执行它，不新增任务，还对结果形成期望
                boolean actionDone = executePrecondition(nal, bestOpWithMeta, concept, projectedGoal, task);
                if(actionDone) {
                    memory.last2Action = memory.lastAction;
                    memory.lastAction = bestOpWithMeta.bestop.term[1].toString();
                    Concept op = memory.concept(bestOpWithMeta.bestop);
                    if(op != null && bestOpWithMeta.executable_precond.sentence.truth.getConfidence()
                            > nar.narParameters.MOTOR_BABBLING_CONFIDENCE_THRESHOLD) {
                        synchronized(op) {
                            op.allowBabbling = false;
                        }
                    }
                    System.out.println("Executed based on: " + bestOpWithMeta.executable_precond);
                    for(ExecutablePrecondition precon0 : anticipationsToMake.get(bestOpWithMeta.bestop)) {
                        float distance = precon0.timeOffset - nal.time.time();
                        float urgency = 2.0f + 1.0f/distance;
                        // 预测推理=试错完善子图
                        ProcessAnticipation.anticipate(nal, precon0.executable_precond.sentence,
                                precon0.executable_precond.budget, precon0.mintime, precon0.maxtime, urgency, precon0.substitution);
                    }
                    // 不要尝试其他表，因为已经使用了特定的解决方案
                    // don't try the other table as a specific solution was already used
//                    return;
                }
            }
        }
        memory.multiAction = false;
    }

    // 中转task，以便lida注意力执行完才到 动作执行
    public static Task task0 = null;
//    public static Narsese narsese = new Narsese(nar);
    private static class ExecutablePrecondition {
        public Operation bestop = null;
        public float bestop_truthexp = 0.0f;
        public TruthValue bestop_truth = null;
        public Task executable_precond = null;
        public long mintime = -1;
        public long maxtime = -1;
        public float timeOffset;
        public Map<Term,Term> substitution;
    }

    private static PamImpl0.PamNodeStructure pamNodeStructure;

    /**
     * Search for the best precondition that best matches recent events, and is most successful in leading to goal fulfilment
     * 寻找与最近的事件最匹配，并且最成功地实现目标的最佳前提条件
     * todo 最近事件太多+遍历太低效弱智=需要更多条件
     * @param nal               The derivation context
     * @param concept0           The goal concept0
     * @param projectedGoal     The goal projected to the current time
     * @param execPreconditions The procedural hypotheses with the executable preconditions 具有可执行先决条件的程序假设
     * @return The procedural hypothesis with the highest result truth expectation 结果期望值最高的过程假设
     */
    public static List<ExecutablePrecondition> calcBestExecutablePrecondition(final DerivationContext nal,
                   final Concept concept0, final Sentence projectedGoal, List<Task> execPreconditions,
                   Map<Operation,List<ExecutablePrecondition>> anticipationsToMake, Task gtask) {
        List<ExecutablePrecondition> actions = new ArrayList<>();
        // 全局调用。初始化mem
        getMem();
        // todo 一般只执行一个满足操作，不能所有都执行
        // 动机竞争所在，一个结果，对应多个备选动作
        // 根据激励度和紧急度的权衡，判断执行时机
        // todo 构建与执行分开，这里是顺推完成后的反推，从本能到中间动作
        // 案例：听到问题--回答问题--满足，听到问题+不开心--不想回答--满足
        for(final Task execPrecondition: execPreconditions){
            boolean shoudComtinue = false;
            Task task = null;
            NeighborhoodAttentionCodelet attentionTask = null;
            Random r = new Random();
            Statement ttd = (Statement) execPrecondition.sentence.term;
            // 循环，双向，如相似对等
            if(gtask.fromGoal != null && gtask.fromGoal.term == ttd.getSubject()) {continue;}
            Statement ii = (Statement) execPrecondition.getTerm();
            Term subject = ii.getSubject();// 前提主语
            Term predicateTerm = ii.getPredicate();

            // 是否复合。t肯定是复合，因为要求statement，是可执行前提
            boolean subIsCompound = subject instanceof CompoundTerm;
            boolean predIsCompound = predicateTerm instanceof CompoundTerm;
            // 是否变量
            boolean subIsVar = subject instanceof Variable;
            boolean predIsVar = predicateTerm instanceof Variable;
            boolean predHasVar = predIsVar || predIsCompound && predicateTerm.hasVar();
            boolean subHasVar = subIsVar || subIsCompound && subject.hasVar();

            // 各种情况：是否复合，是否有时间，是否变量，是否时序（一般带多变量），是否状态（与普通信念不同？），是否操作（可看做简单时序）
            // 操作和状态信念等杂糅，同一句多个类型。todo 要区分序列、合取等情况。区分单结论动机和整句顺承等动机？<(a,b)=/>c>!和c！
            // 单个因变量，单个自变量，多个因变量，多个自变量，复合因变量，复合自变量
            // 1、复合+无变量+状态-->复合+无变量+状态，
            // 2、复合非变量--单个变量，
            // 3、单个变量--复合非变量，
            // 4、单个变量--单个变量

            Map<Term,Term> subsconc = new LinkedHashMap<>();
            // 结论是否匹配，如果匹配，则有subs可替代项，即变量实例化
            boolean conclusionMatches = Variables.findSubstitute(
                    nal.mem().randomNumber,
                    Symbols.VAR_INDEPENDENT,
                    CompoundTerm.replaceIntervals(predicateTerm),
                    CompoundTerm.replaceIntervals(projectedGoal.getTerm()),
                    subsconc,
                    new LinkedHashMap<>());
            if (!conclusionMatches) {continue;}
            if (predHasVar) {
                // 动机如(&&,运算,(&&,26,加,8))。直接用变量实例化，执行时序
                String re =  doVarTask(projectedGoal, subject, predicateTerm, execPrecondition, nal);
                if (re.equals("true")){
                    continue;
                }else if (re.equals("break")){
                    return null;
                }
            }
            if (subHasVar) {
                CompoundTerm ct = (CompoundTerm) execPrecondition.getTerm();
                // 动机如happy。"回应"等两个及以上前提。实例化并提升动机，由动机处理。因为要用实例化后动机实例化时序
                // 这步是提升(&&,运算,(&&,26,加,8))为动机，即替换$命令为(&&,运算,(&&,26,加,8))
                String re =  doVarUnify(ct);

                if (re.equals("true")){
                    execPrecondition.sentence.setKey(ct.toString());
                    continue;
                }else if (re.equals("null")){
                    return null;
                }
            }
            // 下面是无变量，或者变量已实例化，或者不匹配的情况
            final Result result = getResult(nal, projectedGoal, execPrecondition, subject);
            if (result == null) return null;

            // 时序一：可能是动作序列，而不止一个动作，中间没有需要确认的状态
            // 只需要确保动作完成，而不需要确认状态是否实现，比如举手，手真的举起来了吗？精神病？
            List<Operation> ops = new ArrayList<>();
            List<Float> prec_intervals = new ArrayList<>();
            boolean isSingleAc = false;
            boolean isAction = false;
            boolean isState = false;
            String pps = result.precTerm.toString();

            if(!pps.contains("^")) {
                isAction = isAction(result.prec, isAction);
                if (!isAction){
                    // 一连串，纯状态条件，无操作
                    isState = true;
                }
            }

            // 截取中间词项
            MultiAct multiAct = getMultiAct(ops, attentionTask, predicateTerm, task, isSingleAc, result.precTerm, prec_intervals);

            // ok we can look now how much it is fullfilled check recent events in event bag of global buffer as well
            // 我们现在可以看一下完成了多少，检查事件包中的最近事件，全局缓冲区也是如此
            Map<Term,Term> subsBest = new LinkedHashMap<>();
            Task bestsofar = null;
            long newesttime = -1;
            Bag1 seqBag1 = memory.globalBuffer;//.seq_current;

            final BestSofar bestSofar0 = getBestSofar0(nal, seqBag1, newesttime, subsconc, multiAct, prec_intervals, bestsofar, subsBest);

            if(bestSofar0.bestsofar == null) {
                if(isState){
                    // 单前提直接满足目标，多前提再提升中间为目标
                    getDefultTask(projectedGoal, result.precTerm, Tense.Present, Symbols.GOAL_MARK);
//                    return null;
                    continue;
                }
                continue;
            }
            if (isAction){
                Term[] terms = ((CompoundTerm)subject).term;
                getDefultTask(projectedGoal, terms[terms.length - 1], Tense.Present, Symbols.GOAL_MARK);
                break;
            }
            if(isState){
                // 多个状态前提，提升中间为目标
                if(bestSofar0.bestsofar.getTerm().toString().length() != execPrecondition.getTerm().toString().length()){
                    Term[] terms = ((CompoundTerm)subject).term;
                    getDefultTask(projectedGoal, terms[terms.length - 1], Tense.Present, Symbols.GOAL_MARK);
                }else {
                    getDefultTask(null, projectedGoal.term, Tense.Present, Symbols.JUDGMENT_MARK);
                }
                continue;
//                shoudComtinue = true;
            }

            List<ExecutablePrecondition> actions1 = getExecutablePreconditions(nal, concept0, projectedGoal,
                    anticipationsToMake, execPrecondition, bestSofar0, multiAct, result, ops, actions, r);
            if (actions1 != null) return actions1;
        }
        return actions;
    }

    @Nullable
    private static List<ExecutablePrecondition> getExecutablePreconditions(DerivationContext nal, Concept concept1, Sentence projectedGoal0, Map<Operation, List<ExecutablePrecondition>> anticipationsToMake, Task execPrecondition, BestSofar bestSofar0, MultiAct multiAct, Result result, List<Operation> ops, List<ExecutablePrecondition> actions, Random r) {
        //ok now we can take the desire value:  现在我们可以获取期望值：
        final TruthValue A = projectedGoal0.getTruth();//Task.Truth
        //and the truth of the hypothesis:  假设真值：
        final TruthValue Hyp = execPrecondition.sentence.truth;//execPreconditions.truth
        //and derive the conjunction of the left side: 并得出左侧的合取
        final TruthValue leftside = TruthFunctions.desireDed(A, Hyp, nar.narParameters);
        //overlap will almost never happen, but to make sure    //几乎不会发生重叠，但是要确保
        if(Stamp.baseOverlap(projectedGoal0.stamp, execPrecondition.sentence.stamp) ||
            Stamp.baseOverlap(bestSofar0.bestsofar.sentence.stamp, execPrecondition.sentence.stamp) ||
            Stamp.baseOverlap(projectedGoal0.stamp, bestSofar0.bestsofar.sentence.stamp)) {
            return null;
        }
        //and the truth of the precondition: 前提真值：
        final Sentence projectedPrecon = bestSofar0.bestsofar.sentence.projection(nal.time.time() /*- distance*/, nal.time.time(), concept1.memory);
        if(projectedPrecon.isEternal()) {
            // projection wasn't better than eternalization, too long in the past
            // 投影不比永恒化要好，在过去太久了
            return null;
        }
        final TruthValue precon = projectedPrecon.truth;//memory.seq_current.truth

        if (multiAct.isSingleAc) {
            memory.multiAction = false;
            // todo 操作按顺序编排
            for (Term term : result.prec) {
                if (term instanceof Operation) {
                    ops.add((Operation) term);
                }
            }
//                op0 = (Operation) prec[prec.length-2];
//                ops.add(op0);
        }
        actions.clear();

        final List<ExecutablePrecondition> actions1 = getExecutablePreconditions(nal, anticipationsToMake, execPrecondition, precon, leftside, ops, bestSofar0, actions, r, multiAct);
        if (actions1 != null) return actions1;
        return null;
    }

    @Nullable
    private static Result getResult(DerivationContext nal, Sentence projectedGoal, Task execPrecondition, Term subject) {
        Conjunction precTerm = null;
        if(subject instanceof Conjunction){
            precTerm = (Conjunction) subject; //execPreconditions.Sub
        }else if(subject instanceof PamNodeImpl) {
            try {
                Term term = narsese.parseTerm(subject.toString());
                precTerm = getPrecTerm(nal, projectedGoal, execPrecondition, term);
                if (precTerm == null) return null;
            } catch (Parser.InvalidInputException e) {throw new RuntimeException(e);}
        }else {
            try { // (&&,运算,(&&,26,加,8))
                precTerm = getPrecTerm(nal, projectedGoal, execPrecondition, subject);
            } catch (Parser.InvalidInputException e) {
                throw new RuntimeException(e);
            }
            if (precTerm == null) return null;
        }
        final Term[] prec = precTerm.term;
        Result result = new Result(precTerm, prec);
        return result;
    }

    private static class Result {
        public final Conjunction precTerm;
        public final Term[] prec;

        public Result(Conjunction precTerm, Term[] prec) {
            this.precTerm = precTerm;
            this.prec = prec;
        }
    }

    @Nullable
    private static List<ExecutablePrecondition> getExecutablePreconditions(DerivationContext nal, Map<Operation, List<ExecutablePrecondition>> anticipationsToMake, Task execPrecondition, TruthValue precon, TruthValue leftside, List<Operation> ops, BestSofar bestSofar0, List<ExecutablePrecondition> actions, Random r, MultiAct multiAct) {
        float timeWindowHalf = 10.0f * nar.narParameters.ANTICIPATION_TOLERANCE;
//            Term pp = prec[prec.length - 1];
//            if (pp instanceof Interval) {
//                timeOffset = (long) ((Interval)pp).time;
//                timeWindowHalf = timeOffset * nar.narParameters.ANTICIPATION_TOLERANCE;
//            }
        //and derive the conjunction of the left side:  并导出左侧的合取：
        //in order to derive the operator desire value: 为了得出算子的期望值：
        final TruthValue opdesire = TruthFunctions.desireDed(precon, leftside, nar.narParameters);
        final float expecdesire = opdesire.getExpectation();
        long mintime = (long) (nal.time.time() + 10.0f - timeWindowHalf);
        long maxtime = (long) (nal.time.time() + 10.0f + timeWindowHalf);
        Operation bestop;
        ExecutablePrecondition action;
        String opName;
        String lastAc;
        for(Operation op: ops){
            bestop = (Operation) op.applySubstitute(bestSofar0.subsBest);
            action = new ExecutablePrecondition();
            if(expecdesire > action.bestop_truthexp) {
                action.bestop = bestop;
                action.bestop_truthexp = expecdesire;
                action.bestop_truth = opdesire;
                action.executable_precond = execPrecondition;
                action.substitution = bestSofar0.subsBest;
                action.mintime = mintime;
                action.maxtime = maxtime;
                action.timeOffset = 10.0f;
                if(anticipationsToMake.get(action.bestop) == null) {
                    anticipationsToMake.put(action.bestop, new ArrayList<>());
                }
                anticipationsToMake.get(action.bestop).add(action);
            }
            actions.add(action);
            opName = action.bestop.term[1].toString();
            int lastAction = r.nextInt(2);
            if (lastAction == 1) {
                lastAc = memory.lastAction;
            }else {
                lastAc = memory.last2Action;
            }
            // 如果不是序列动作，并大于阈值且不与上次动作相同，避免全部遍历完才得出较好动作选择
            if (!memory.multiAction && action.bestop_truthexp > 0.51f && !opName.equals(lastAc)) {
                if (multiAct.attentionTask != null) {
                    pam.getAssistingTaskSpawner().addTask(multiAct.attentionTask);
                }
                // 任务数余留大于50，不至于执行太快，lida注意力跟不上，先状态后动作
                if(multiAct.task != null && memory.globalBuffer.nameSize() > 1350){
                    memory.inputTask(nar, multiAct.task);
                }else if (multiAct.task != null && memory.globalBuffer.nameSize() <= 1350) {
                    task0 = multiAct.task;
                }else {
                    task0 = null;
                }
                return actions;
            }
        }
        // 如果是序列动作，都大于阈值
        if (memory.multiAction && actions.size() > 1 && actions.get(0).bestop_truthexp > 0.51f &&  actions.get(1).bestop_truthexp > 0.51f) {
            if (multiAct.attentionTask != null) {
                pam.getAssistingTaskSpawner().addTask(multiAct.attentionTask);
            }
            if(multiAct.task != null){
                memory.inputTask(nar, multiAct.task);
            }
            return actions;
        }
        return null;
    }

    @NotNull
    private static ProcessGoal.BestSofar getBestSofar0(DerivationContext nal, Bag1 seqBag1, long newesttime, Map<Term, Term> subsconc,
                                                       MultiAct multiAct, List<Float> prec_intervals, Task bestsofar, Map<Term, Term> subsBest) {
        synchronized(seqBag1) {
            for(final Object p1 : seqBag1) {
                Task p = (Task) p1;
                if(p.sentence.isJudgment()
                        && !p.sentence.isEternal()
                        && p.sentence.getOccurenceTime() > newesttime
                        && p.sentence.getOccurenceTime() <= nal.time.time()
                ) {
                    Map<Term,Term> subs = new LinkedHashMap<>(subsconc);
                    // 前提是否匹配，结论实例化的变量subs，传下来
                    // todo 多个变量，多个前提，多个结论等情况
                    boolean preconditionMatches = Variables.findSubstitute(memory.randomNumber, Symbols.VAR_INDEPENDENT,
                        CompoundTerm.replaceIntervals(multiAct.precondition),
                        CompoundTerm.replaceIntervals(p.sentence.term), subs, new LinkedHashMap<>());
                    // 结论前提都匹配 = 夹着的动作才执行 。 // || term.contains("^eat")
                    if(preconditionMatches){
                        Task pNew = new Task(p.sentence.clone(),
                                p.budget.clone(),
                                p.isInput() ? Task.EnumType.INPUT : Task.EnumType.DERIVED);
                        newesttime = p.sentence.getOccurenceTime();
                        // Apply interval penalty for interval differences in the precondition
                        // 对先决条件中的间隔差异应用间隔惩罚
                        LocalRules.intervalProjection(nal, pNew.sentence.term, multiAct.precondition, prec_intervals, pNew.sentence.truth);
                        bestsofar = pNew;
                        subsBest = subs;
                    }
                }
            }
        }
        BestSofar bestSofar0 = new BestSofar(subsBest, bestsofar);
        return bestSofar0;
    }

    private static class BestSofar{
        public final Map<Term, Term> subsBest;
        public final Task bestsofar;

        public BestSofar(Map<Term, Term> subsBest, Task bestsofar) {
            this.subsBest = subsBest;
            this.bestsofar = bestsofar;
        }
    }

    private static boolean isAction(Term[] prec, boolean isAction) {
        if(prec[prec.length - 1] instanceof CompoundTerm) {
            // 目前默认最后一个
            CompoundTerm plastct = (CompoundTerm) prec[prec.length - 1];
            for (Term tt: plastct.term){
                // 有操作，不是状态。但不是最底层的具身动作，可能具身，但需要组合即具身时序，可能是心理时序
                // todo 直接查图谱
                if (AgentStarter.actmap.containsKey(tt.toString()) || tt.toString().equals("回应")){
                    isAction = true;
                    break;
                }
            }
        }
        return isAction;
    }

    private static String doVarUnify(CompoundTerm ct) {
        boolean is = PamImpl0.isAllInMemVar(ct);
        return is ? "true" : "false";
    }

    private static String doVarTask(Sentence projectedGoal1, Term subject, Term predicateTerm, Task execPrecondition, DerivationContext nal) {
        // 变量时序（execPrecondition）代换并执行。如:goal=(25,add,8),execPrecondition=($x,add,$y)。goal也有单独【$命令】等？
        // 分别用t的subject、predicate匹配目标的整个term，如果是变量则忽略
        // todo 区分信念和时序，作为节点信念可能激活，但时序还需执行，才最终满足。如：(25,add,8)!
        // todo $命令，（回应，$问题)等，带变量动机可提升，但动机处理时，除了要实例化，还要本身执行，而不是直接往前反推
        // todo 同一执行阶段，不同执行方案是竞争关系。不同阶段的同一执行方案是序列关系
        Term seqTerm = null;
        Map<Term,Term> subsconc = new LinkedHashMap<>();
        Map<Term,Term> subsconc2 = new LinkedHashMap<>();
        Map<Term,Term> subsconc3 = new LinkedHashMap<>();
        Map<Term,Term> subsconc4 = new LinkedHashMap<>();
        // 结论是否匹配，如果匹配，则有subs可替代项，即变量实例化
        boolean subMatches = Variables.findSubstitute(memory.randomNumber, Symbols.VAR_INDEPENDENT,
                CompoundTerm.replaceIntervals(subject),
                CompoundTerm.replaceIntervals(projectedGoal1.getTerm()), subsconc, subsconc2);
        // execPrecondition，相似语句 如：<nars_4 <-> (&&,运算,(&&,$加数1,加,$加数2))>.
        // <(^say,(^search,(*,$事物,去,哪里,了))) <-> (*,回应,(*,$事物,去,哪里,了))>.
        boolean predMatches = Variables.findSubstitute(memory.randomNumber, Symbols.VAR_INDEPENDENT,
                CompoundTerm.replaceIntervals(predicateTerm),
                CompoundTerm.replaceIntervals(projectedGoal1.getTerm()), subsconc3, subsconc4);
        // 将匹配到的实例化，makeNowisa
        if (subMatches) {
            for (Term term: subsconc.keySet()) {
                makeIsa(term.toString(), 20001, subsconc.get(term).toString());
            }
            // 当前一半变量在实例化，时序为另一半
            seqTerm = Variables.applySubstituteAndRenameVariables((CompoundTerm) predicateTerm,subsconc);
        }
        if (predMatches) {
            for (Term term: subsconc3.keySet()) {
                makeIsa(term.toString(), 20001, subsconc3.get(term).toString());
            }
            seqTerm = Variables.applySubstituteAndRenameVariables((CompoundTerm) subject,subsconc3);
        }
        if (seqTerm != null && execPrecondition.getTerm() instanceof Similarity) {
            // && seqTerm.toString().contains("nar")。目前默认相似变量句为时序执行，pred为非变量
            // 非时序执行跳出。如果是多个前提的变量句，需要实例化，直接送入变量推理规则？pam激活变量句时即可实例化
            // todo 可按需联合nars执行，关键在子时序拆解
            // 实例化后只有一个操作，直接执行，如(^say,(^search,(*,$事物,去,哪里,了)))
            if (seqTerm instanceof Operation) {
                Operation op = (Operation) seqTerm;
                final Sentence createdSentence = new Sentence(
                        op,
                        Symbols.GOAL_MARK,
                        execPrecondition.sentence.truth,
                        projectedGoal1.stamp);
                final Task t0 = new Task(
                        createdSentence,
                        new BudgetValue(1.0f,1.0f,1.0f, nar.narParameters),
                        Task.EnumType.DERIVED);
                t0.fromGoal = projectedGoal1;
                executeOperation(nal, t0);
                return "op";
            }
            Link link = TermUtil.termToLink1(projectedGoal1.term, seqTerm, "");
            // 时间戳，以当前时间为准，作为整个大时序的唯一标识，只在这里新建
            // 当前执行轮次=以时间戳为准，避免不同轮次的时序混乱，特别是参数表维护
            String actStamp = String.valueOf(System.currentTimeMillis());

            // todo 时序实例化需要一式四份？csm、nars、动机、时序，先测试单时序
            // 先溯源，找到传递下来的参数集（变量句构建nowisa），然后在时序buffer里全量or单步构建框架，实例化，然后逐步执行
            // 没有可执行方案，但可能是状态或时序。从时序首开始执行，递归查找到最上头时序
            // todo 一步步推理拆分，可能时间轴拉得很长，中间有很多其他任务。
            // 时序=强关联结构，可以直接执行，不用推理。特别是长语句，没法用字符串显式表达
            // 先提时序首为动机，然后父动机再进入，先进先出，单思维先执行完子动机？靠反馈结果再激活父动机？
            // todo 直接输出变量式描述，也就是抽象时序本身，非实例化后的时序
            pam.getActRoot(link, true, false, actStamp);
            // 完成了，后续不用执行
            return "break";
        }
        return "false";
    }

    private static void makeIsa(String term, int id, String subsconc3) {
        PamNode type;
        // 如果term同时有开合括号，不去掉，只有一个括号，去掉
        if (!term.contains("(") || !term.contains(")")) {
            term = term.replace("(", "").replace(")", "");
        }
        type = TermUtil.getPamNode(term, id);
        if (!subsconc3.contains("(") || !subsconc3.contains(")")) {
            subsconc3 = subsconc3.replace("(", "").replace(")", "");
        }
        PamNode value = TermUtil.getPamNode(subsconc3, id);
        IsaPamTask.makeNowisa(type, value);
    }

    @Nullable
    private static Conjunction getPrecTerm(DerivationContext nal, Sentence projectedGoal2, Task t, Term term) throws Parser.InvalidInputException {
        Conjunction precTerm;
        if(term instanceof Conjunction) {
            precTerm = (Conjunction) term;
        }else if (term instanceof Operator) {
            // todo 单个操作，无self，与有self的operation等效，可统一，如lida的turnleft
            Operation op = Operation.make((Operator) term,new Term[]{narsese.parseTerm("{SELF}")},true);
            // 直接执行
            final Sentence createdSentence = new Sentence(
                    op,
                    Symbols.GOAL_MARK,
                    t.sentence.truth,
                    projectedGoal2.stamp);
            final Task t0 = new Task(
                    createdSentence,
                    new BudgetValue(1.0f,1.0f,1.0f, nar.narParameters),
                    Task.EnumType.DERIVED);
            t0.fromGoal = projectedGoal2;
            executeOperation(nal, t0);
            return null;
        }else {
            // todo 单词项单前提的状态或操作对等词（如【坐】关联【^坐】）
            // 也要经过下面的前提结论匹配？若非具身操作，不能执行。直接提升为目标后，由目标自己处理
            final Stamp stamp = new Stamp(nar.time() , Tense.Present, memory.newStampSerial(),
                    AgentStarter.nar.narParameters.DURATION);
            final Sentence sentence = new Sentence(term, Symbols.GOAL_MARK,
                    new TruthValue((float) 1.0, 0.9, AgentStarter.nar.narParameters), stamp);
            final BudgetValue budget = new BudgetValue(0.8f, 0.5f, 1, AgentStarter.nar.narParameters);

            Task stateTask0 = new Task(sentence, budget, Task.EnumType.DERIVED);
            stateTask0.fromGoal = projectedGoal2;
            memory.addNewTask(stateTask0, "lida");
            memory.doAll(nar);
            return null;
        }
        return precTerm;
    }

    @NotNull
    private static ProcessGoal.MultiAct getMultiAct(List<Operation> ops, NeighborhoodAttentionCodelet attentionTask,
                                                    Term predicateTerm, Task task, boolean isSingleAc, Conjunction precTerm, List<Float> prec_intervals) {
        Term precondition;
        Term[] newprec;
        Term[] prec = precTerm.term;
        // 遍历prec，看是否有间隔travel
        boolean isTravel = false;
        for (Term term : prec) {
            if (term instanceof Interval) {
                isTravel = true;
                break;
            }
        }
        // 多种序列情况
        if (prec.length > 2 && isTravel) {
            int index = prec.length - 3;
            newprec = new Term[index];
            System.arraycopy(prec, 0, newprec, 0, index);
            precondition = Conjunction.make(newprec,TemporalRules.ORDER_FORWARD);
            if(prec[2] instanceof Operation){
                index = 1;
                memory.multiAction = true;
                for (int i = 2; i <= prec.length - 2; i += 2) {
                    // 单前提多连续动作
                    if (prec[i] instanceof Operation) {
                        ops.add((Operation) prec[i]);
                    }else {
                        // 单前提多间隔动作
                        memory.multiAction = false;
                        if (!prec[i].name().toString().contains("foodOrigin")) {break;}

                        String sought = ((CompoundTerm)((CompoundTerm)((CompoundTerm) prec[i])
                                .term[0]).term[0]).term[0].name().toString();
                        attentionTask = new NeighborhoodAttentionCodelet(sought);

                        Term[] newprec0 = new Term[prec.length - i];
                        System.arraycopy(prec, i, newprec0, 0, prec.length-i);
                        Implication im = Implication.make(Conjunction.make(newprec0,TemporalRules.ORDER_FORWARD),
                                predicateTerm,1);

                        Stamp stamp = new Stamp(-1 , Tense.Future, memory.newStampSerial(), nar.narParameters.DURATION);
                        Sentence sentence = new Sentence(im, '.',
                                new TruthValue((float) 1.0, 0.9, true, nar.narParameters), stamp);
                        BudgetValue budget = new BudgetValue(0.99f, 0.99f, 1, nar.narParameters);
                        task = new Task(sentence, budget, Task.EnumType.INPUT);
                        break;
                    }
                }
            }else {
                //多前提 todo 继续细分 多前提多连续动作，多前提多间隔动作，多前提单动作
                isSingleAc = true;
                index = 2;
            }
            for(Long l : CompoundTerm.extractIntervals(memory, precTerm)) {
                prec_intervals.add((float) l);
            }
        }else {
            // 单前提单动作
            isSingleAc = true;
            if (prec[0] instanceof CompoundTerm) {
//                precondition = precTerm.term[0];
                int index = prec.length - 1;
                newprec = new Term[index];
                System.arraycopy(prec, 0, newprec, 0, index);
                precondition = Conjunction.make(newprec);
            }else {
                precondition = precTerm;
            }
        }
        MultiAct multiAct0 = new MultiAct(attentionTask, task, precondition, isSingleAc);
        return multiAct0;
    }

    private static class MultiAct {
        public final NeighborhoodAttentionCodelet attentionTask;
        public final Task task;
        public final Term precondition;
        public final boolean isSingleAc;

        public MultiAct(NeighborhoodAttentionCodelet attentionTask, Task task, Term precondition, boolean isSingleAc) {
            this.attentionTask = attentionTask;
            this.task = task;
            this.precondition = precondition;
            this.isSingleAc = isSingleAc;
        }
    }

    /**
     * Execute the operation suggested by the most applicable precondition
     *  执行最适用的前提条件建议的操作
     * @param nal The derivation context
     * @param precon1 The procedural hypothesis leading to goal
     * @param concept The concept of the goal
     * @param projectedGoal The goal projected to the current time
     * @param task The goal task
     */
    private static boolean executePrecondition(final DerivationContext nal, ExecutablePrecondition precon1,
                                               final Concept concept, final Sentence projectedGoal,
                                               final Task task) {/*&& Math.random() < bestop_truthexp */
        if(precon1.bestop != null && precon1.bestop_truthexp > nar.narParameters.DECISION_THRESHOLD ) {
            final Sentence createdSentence = new Sentence(
                precon1.bestop,
                Symbols.GOAL_MARK,
                precon1.bestop_truth,
                projectedGoal.stamp);
            final Task t = new Task(
                    createdSentence,
                    new BudgetValue(1.0f,1.0f,1.0f, nar.narParameters),
                    Task.EnumType.DERIVED);
            //System.out.println("used " +t.getTerm().toString() + String.valueOf(memory.randomNumber.nextInt()));
            if(!task.sentence.stamp.evidenceIsCyclic()) { // 任务，句子，证据，证据是循环的
                if(!executeOperation(nal, t)) { //this task is just used as dummy
                    concept.memory.emit(Events.UnexecutableGoal.class, task, concept, nal);
                    return false;
                }
                return true;
            }
        }
        return false;
    }

    /**
     * Entry point for all potentially executable operation tasks.
     * Returns true if the Task has a Term which can be executed
     * 所有潜在的可执行操作任务的入口点。 如果任务具有可以执行的词项，则返回true
     * @param nal The derivation concept
     * @param t The operation goal task
     */
    public static boolean executeOperation(final DerivationContext nal, final Task t) {
        final Term content = t.getTerm();
        // 如果已经执行一次动作，而且不是连续序列动作，则跳出
        if(!(memory.allowExecution) && !(memory.multiAction) || (!(content instanceof Operation) && !(content instanceof Operator))) {
            return false;
        }
        final Operator oper;
//        if (content instanceof Operation){
            final Operation op = (Operation)content;
            oper = op.getOperator();
//        }else if (content instanceof Operator) {
//            oper = (Operator) content;
//        }

        final Product prod = (Product) op.getSubject();
        final Term arg = prod.term[0];
        // 同步执行（阻塞，在线程中）
        if(oper instanceof FunctionOperator) {
            //except last one, the output arg //除了最后一个，输出arg
            for(int i = 0;i < prod.term.length - 1;i++) {
                if(prod.term[i].hasVarDep() || prod.term[i].hasVarIndep()) {
                    return false;
                }
            }
        } else {
            if(content.hasVarDep() || content.hasVarIndep()) {
                if (!content.toString().contains("^search")){
                    System.out.println("-----有变量-----not say---: " + content);
                    return false;
                }
            }
        }
        //will be deprecated in the future //将来会不推荐使用
//        if(!arg.equals(Term.SELF)) {
//            return false;
//        }
        op.setTask(t);
        // 直接执行动作，不需要新任务
        if(!oper.call(op, memory, nal.time)) {
            return false;
        }
        if (Debug.DETAILED) {
            System.out.println(t.toStringLong());
        }
        return true;
    }
}
